'use client';

import { useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { usePathname, useSearchParams } from 'next/navigation';
import { 
  trackEvent, 
  trackPageView, 
  trackOrderEvent, 
  trackBlogEvent, 
  trackUserEvent 
} from '@/components/analytics/GoogleAnalytics';
import { 
  pushToDataLayer, 
  trackGTMOrderEvent, 
  trackGTMUserEvent, 
  trackGTMBlogEvent 
} from '@/components/analytics/GoogleTagManager';
import { claritySet, clarityEvent } from '@/components/analytics/MicrosoftClarity';

export function useEnhancedAnalytics() {
  const { data: session, status } = useSession();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Track page views
  useEffect(() => {
    if (status === 'loading') return;

    const url = `${pathname}${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    const title = document.title;

    // Track page view in Google Analytics
    trackPageView(url, title);

    // Track page view in GTM
    pushToDataLayer('page_view', {
      page_path: pathname,
      page_title: title,
      user_type: session?.user?.role || 'anonymous',
      user_id: session?.user?.id || null
    });

    // Set user context in Clarity
    if (session?.user) {
      claritySet('user_type', session.user.role || 'unknown');
      claritySet('user_id', session.user.id || 'anonymous');
    }
  }, [pathname, searchParams, session, status]);

  // Track user events
  const trackUser = useCallback((eventType: string, data?: Record<string, unknown>) => {
    const userData = {
      user_type: session?.user?.role || 'anonymous',
      user_id: session?.user?.id || null,
      ...data
    };

    trackUserEvent(eventType as any, userData);
    trackGTMUserEvent(eventType, userData);
    clarityEvent(`user_${eventType}`);
  }, [session]);

  // Track order events
  const trackOrder = useCallback((eventType: string, orderData?: Record<string, unknown>) => {
    const data = {
      user_type: session?.user?.role || 'anonymous',
      user_id: session?.user?.id || null,
      timestamp: new Date().toISOString(),
      ...orderData
    };

    trackOrderEvent(eventType as any, data);
    trackGTMOrderEvent(eventType, data);
    clarityEvent(`order_${eventType}`);
  }, [session]);

  // Track blog events
  const trackBlog = useCallback((eventType: string, blogData?: Record<string, unknown>) => {
    const data = {
      user_type: session?.user?.role || 'anonymous',
      user_id: session?.user?.id || null,
      page_path: pathname,
      ...blogData
    };

    trackBlogEvent(eventType as any, data);
    trackGTMBlogEvent(eventType, data);
    clarityEvent(`blog_${eventType}`);
  }, [session, pathname]);

  // Track custom events
  const trackCustomEvent = useCallback((eventName: string, eventData?: Record<string, unknown>) => {
    const data = {
      user_type: session?.user?.role || 'anonymous',
      user_id: session?.user?.id || null,
      page_path: pathname,
      timestamp: new Date().toISOString(),
      ...eventData
    };

    trackEvent(eventName, data);
    pushToDataLayer(eventName, data);
    clarityEvent(eventName);
  }, [session, pathname]);

  // Track form interactions
  const trackFormEvent = useCallback((formType: string, action: string, formData?: Record<string, unknown>) => {
    trackCustomEvent(`form_${action}`, {
      form_type: formType,
      ...formData
    });
  }, [trackCustomEvent]);

  // Track button clicks
  const trackButtonClick = useCallback((buttonType: string, buttonData?: Record<string, unknown>) => {
    trackCustomEvent('button_click', {
      button_type: buttonType,
      ...buttonData
    });
  }, [trackCustomEvent]);

  // Track search events
  const trackSearch = useCallback((searchTerm: string, searchData?: Record<string, unknown>) => {
    trackCustomEvent('search', {
      search_term: searchTerm,
      ...searchData
    });
  }, [trackCustomEvent]);

  // Track file downloads
  const trackDownload = useCallback((fileName: string, fileType: string, downloadData?: Record<string, unknown>) => {
    trackCustomEvent('file_download', {
      file_name: fileName,
      file_type: fileType,
      ...downloadData
    });
  }, [trackCustomEvent]);

  // Track external link clicks
  const trackExternalLink = useCallback((url: string, linkData?: Record<string, unknown>) => {
    trackCustomEvent('external_link_click', {
      external_url: url,
      ...linkData
    });
  }, [trackCustomEvent]);

  return {
    trackUser,
    trackOrder,
    trackBlog,
    trackCustomEvent,
    trackFormEvent,
    trackButtonClick,
    trackSearch,
    trackDownload,
    trackExternalLink
  };
}

// Specific hooks for different sections
export function useOrderAnalytics() {
  const { trackOrder } = useEnhancedAnalytics();
  
  return {
    trackOrderStarted: (orderData?: Record<string, unknown>) => trackOrder('order_started', orderData),
    trackOrderCompleted: (orderData?: Record<string, unknown>) => trackOrder('order_completed', orderData),
    trackOrderCancelled: (orderData?: Record<string, unknown>) => trackOrder('order_cancelled', orderData),
    trackOrderUpdated: (orderData?: Record<string, unknown>) => trackOrder('order_updated', orderData),
  };
}

export function useBlogAnalytics() {
  const { trackBlog } = useEnhancedAnalytics();
  
  return {
    trackBlogView: (blogData?: Record<string, unknown>) => trackBlog('blog_view', blogData),
    trackBlogShare: (platform: string, blogData?: Record<string, unknown>) => 
      trackBlog('blog_share', { platform, ...blogData }),
    trackBlogEngagement: (engagementType: string, blogData?: Record<string, unknown>) => 
      trackBlog('blog_engagement', { engagement_type: engagementType, ...blogData }),
  };
}

export function useUserAnalytics() {
  const { trackUser } = useEnhancedAnalytics();
  
  return {
    trackUserRegistration: (userData?: Record<string, unknown>) => trackUser('user_registration', userData),
    trackUserLogin: (userData?: Record<string, unknown>) => trackUser('user_login', userData),
    trackUserLogout: (userData?: Record<string, unknown>) => trackUser('user_logout', userData),
    trackProfileUpdate: (userData?: Record<string, unknown>) => trackUser('profile_update', userData),
  };
}
